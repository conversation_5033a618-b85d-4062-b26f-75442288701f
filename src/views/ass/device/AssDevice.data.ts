import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '序列号',
    align: 'center',
    dataIndex: 'snNo',
  },
  {
    title: '设备品号',
    align: 'center',
    dataIndex: 'deviceCode',
  },
  {
    title: '设备名称',
    align: 'center',
    dataIndex: 'deviceName',
  },
  {
    title: '设备规格',
    align: 'center',
    dataIndex: 'deviceSpecification',
  },

  {
    title: '设备分类',
    align: 'center',
    dataIndex: 'category',
  },
  {
    title: '客户ID',
    align: 'center',
    dataIndex: 'customerId',
  },
  {
    title: '客户编号',
    align: 'center',
    dataIndex: 'customerCode',
  },
  {
    title: '客户名称',
    align: 'center',
    dataIndex: 'customerName',
  },
  {
    title: '供应商ID',
    align: 'center',
    dataIndex: 'supplierId',
  },
  {
    title: '供应商编号',
    align: 'center',
    dataIndex: 'supplierCode',
  },
  {
    title: '供应商名称',
    align: 'center',
    dataIndex: 'supplierName',
  },
  {
    title: '出厂日期',
    align: 'center',
    dataIndex: 'productionDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '保修到期日期',
    align: 'center',
    dataIndex: 'expireDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '验收日期',
    align: 'center',
    dataIndex: 'acceptanceDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '设备状态',
    align: 'center',
    dataIndex: 'status',
  },
  {
    title: '售后负责人',
    align: 'center',
    dataIndex: 'assCharger',
  },
  {
    title: '服务站',
    align: 'center',
    dataIndex: 'assDept',
  },
  {
    title: '产线Id',
    align: 'center',
    dataIndex: 'lineId',
  },
  {
    title: '销售单号',
    align: 'center',
    dataIndex: 'salesOrderNo',
  },
  {
    title: '项目号',
    align: 'center',
    dataIndex: 'projectNo',
  },
  {
    title: '项目号',
    align: 'center',
    dataIndex: 'projectNo',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '设备编号',
    field: 'deviceCode',
    component: 'Input',
  },
  {
    label: '设备名称',
    field: 'deviceName',
    component: 'Input',
  },
  {
    label: '设备规格',
    field: 'deviceSpecification',
    component: 'Input',
  },
  {
    label: '序列号',
    field: 'snNo',
    component: 'Input',
  },
  {
    label: '设备分类',
    field: 'category',
    component: 'Input',
  },
  {
    label: '客户ID',
    field: 'customerId',
    component: 'Input',
  },
  {
    label: '客户编号',
    field: 'customerCode',
    component: 'Input',
  },
  {
    label: '客户名称',
    field: 'customerName',
    component: 'Input',
  },
  {
    label: '供应商ID',
    field: 'supplierId',
    component: 'Input',
  },
  {
    label: '供应商编号',
    field: 'supplierCode',
    component: 'Input',
  },
  {
    label: '供应商名称',
    field: 'supplierName',
    component: 'Input',
  },
  {
    label: '出厂日期',
    field: 'productionDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '保修到期日期',
    field: 'expireDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '验收日期',
    field: 'acceptanceDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '设备状态',
    field: 'status',
    component: 'Input',
  },
  {
    label: '售后负责人',
    field: 'assCharger',
    component: 'Input',
  },
  {
    label: '服务站',
    field: 'assDept',
    component: 'Input',
  },
  {
    label: '产线Id',
    field: 'lineId',
    component: 'Input',
    show: false,
  },
  {
    label: '销售单号',
    field: 'salesOrderNo',
    component: 'Input',
  },
  {
    label: '项目号',
    field: 'projectNo',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  deviceCode: { title: '设备编号', order: 0, view: 'text', type: 'string' },
  deviceName: { title: '设备名称', order: 1, view: 'text', type: 'string' },
  deviceSpecification: { title: '设备规格', order: 2, view: 'text', type: 'string' },
  snNo: { title: '序列号', order: 3, view: 'text', type: 'string' },
  category: { title: '设备分类', order: 4, view: 'text', type: 'string' },
  customerId: { title: '客户ID', order: 5, view: 'text', type: 'string' },
  customerCode: { title: '客户编号', order: 6, view: 'text', type: 'string' },
  customerName: { title: '客户名称', order: 7, view: 'text', type: 'string' },
  supplierId: { title: '供应商ID', order: 8, view: 'text', type: 'string' },
  supplierCode: { title: '供应商编号', order: 9, view: 'text', type: 'string' },
  supplierName: { title: '供应商名称', order: 10, view: 'text', type: 'string' },
  productionDate: { title: '出厂日期', order: 11, view: 'date', type: 'string' },
  expireDate: { title: '保修到期日期', order: 12, view: 'date', type: 'string' },
  acceptanceDate: { title: '验收日期', order: 13, view: 'date', type: 'string' },
  status: { title: '设备状态', order: 14, view: 'text', type: 'string' },
  assCharger: { title: '售后负责人', order: 15, view: 'text', type: 'string' },
  assDept: { title: '服务站', order: 16, view: 'text', type: 'string' },
  lineId: { title: '产线Id', order: 17, view: 'text', type: 'string' },
  salesOrderNo: { title: '销售单号', order: 18, view: 'text', type: 'string' },
  projectNo: { title: '项目号', order: 19, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
