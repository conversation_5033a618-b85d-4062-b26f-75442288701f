package org.jeecg.modules.ass.service.impl;

import org.jeecg.modules.ass.entity.AssDevice;
import org.jeecg.modules.ass.mapper.AssDeviceMapper;
import org.jeecg.modules.ass.service.IAssDeviceService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 设备管理
 * @Author: jeecg-boot
 * @Date:   2025-06-02
 * @Version: V1.0
 */
@Service
public class AssDeviceServiceImpl extends ServiceImpl<AssDeviceMapper, AssDevice> implements IAssDeviceService {

}
