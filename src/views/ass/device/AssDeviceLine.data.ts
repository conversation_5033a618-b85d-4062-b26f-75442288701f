import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '产线编号',
    align:"center",
    dataIndex: 'code'
   },
   {
    title: '产线名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '负责人',
    align:"center",
    dataIndex: 'assCharger'
   },
   {
    title: '服务站',
    align:"center",
    dataIndex: 'assDept'
   },
   {
    title: '销售单号',
    align:"center",
    dataIndex: 'salesOrderNo'
   },
   {
    title: '项目号',
    align:"center",
    dataIndex: 'projectNo'
   },
   {
    title: '出厂日期',
    align:"center",
    dataIndex: 'productionDate'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '产线编号',
    field: 'code',
    component: 'Input',
  },
  {
    label: '产线名称',
    field: 'name',
    component: 'Input',
  },
  {
    label: '负责人',
    field: 'assCharger',
    component: 'Input',
  },
  {
    label: '服务站',
    field: 'assDept',
    component: 'Input',
  },
  {
    label: '销售单号',
    field: 'salesOrderNo',
    component: 'Input',
  },
  {
    label: '项目号',
    field: 'projectNo',
    component: 'Input',
  },
  {
    label: '出厂日期',
    field: 'productionDate',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  code: {title: '产线编号',order: 0,view: 'text', type: 'string',},
  name: {title: '产线名称',order: 1,view: 'text', type: 'string',},
  assCharger: {title: '负责人',order: 2,view: 'text', type: 'string',},
  assDept: {title: '服务站',order: 3,view: 'text', type: 'string',},
  salesOrderNo: {title: '销售单号',order: 4,view: 'text', type: 'string',},
  projectNo: {title: '项目号',order: 5,view: 'text', type: 'string',},
  productionDate: {title: '出厂日期',order: 6,view: 'datetime', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}