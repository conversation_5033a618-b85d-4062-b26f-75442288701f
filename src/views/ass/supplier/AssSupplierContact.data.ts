import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '姓名',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '联系人部门',
    align:"center",
    dataIndex: 'deptName'
   },
   {
    title: '联系电话1',
    align:"center",
    dataIndex: 'phoneNumber1'
   },
   {
    title: '联系电话2',
    align:"center",
    dataIndex: 'phoneNumber2'
   },
   {
    title: '岗位',
    align:"center",
    dataIndex: 'position'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remark'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '姓名',
    field: 'name',
    component: 'Input',
  },
  {
    label: '联系人部门',
    field: 'deptName',
    component: 'Input',
  },
  {
    label: '联系电话1',
    field: 'phoneNumber1',
    component: 'Input',
  },
  {
    label: '联系电话2',
    field: 'phoneNumber2',
    component: 'Input',
  },
  {
    label: '岗位',
    field: 'position',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '姓名',order: 0,view: 'text', type: 'string',},
  deptName: {title: '联系人部门',order: 1,view: 'text', type: 'string',},
  phoneNumber1: {title: '联系电话1',order: 2,view: 'text', type: 'string',},
  phoneNumber2: {title: '联系电话2',order: 3,view: 'text', type: 'string',},
  position: {title: '岗位',order: 4,view: 'text', type: 'string',},
  remark: {title: '备注',order: 5,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}